import { Bookmark, ClientMeta } from '../types';
import { AuthManager } from '../utils/auth';
import { supabase } from '../lib/supabase';
import { env } from '../config/env';

export class BookmarkAPI {
  /**
   * Add bookmark via n8n (Phase 1 - Two-phase ingestion)
   * Sends essential metadata for bookmark card display and server processing
   */
  static async addBookmark(clientMeta: ClientMeta): Promise<Bookmark> {
    try {
      const authHeader = await AuthManager.getAuthHeader();

      // Prepare simplified payload for n8n server
      const payload = {
        url: clientMeta.url,
        client_meta: {
          title: clientMeta.title,
          summary: clientMeta.summary,
          logo_url: clientMeta.logo_url,
          domain: clientMeta.domain,
          timestamp: clientMeta.timestamp,
          is_bookmarkable: clientMeta.isBookmarkable,
        },
      };

      console.log('📤 Sending bookmark to n8n:', payload);

      const response = await fetch(`${env.n8n.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('UNAUTHORIZED');
        }
        throw new Error(`Failed to add bookmark: ${response.statusText}`);
      }

      const bookmark = await response.json();
      return this.normalizeBookmark(bookmark);
    } catch (error) {
      console.error('Failed to add bookmark via n8n:', error);
      throw error;
    }
  }

  /**
   * Get inbox bookmarks (unarchived, sorted by unread first then by date)
   */
  static async getInboxBookmarks(): Promise<Bookmark[]> {
    try {
      const session = await AuthManager.requireSession();

      const { data, error } = await supabase
        .from('bookmarks')
        .select('*')
        .eq('user_id', session.user.id)
        .eq('is_archived', false)
        .order('last_open', { ascending: true, nullsFirst: true }) // null first (unread), then by date
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        if (error.code === 'PGRST301') { // JWT expired
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }

      return (data || []).map(this.normalizeBookmark);
    } catch (error) {
      console.error('Failed to get inbox bookmarks:', error);
      throw error;
    }
  }

  /**
   * Get archived bookmarks (sorted by date)
   */
  static async getArchivedBookmarks(): Promise<Bookmark[]> {
    try {
      const session = await AuthManager.requireSession();

      // Ensure Supabase client is using the correct session
      await supabase.auth.setSession({
        access_token: session.access_token,
        refresh_token: session.refresh_token,
      });

      const { data, error } = await supabase
        .from('bookmarks')
        .select('*')
        .eq('user_id', session.user.id)
        .eq('is_archived', true)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        if (error.code === 'PGRST301') { // JWT expired
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }

      return (data || []).map(this.normalizeBookmark);
    } catch (error) {
      console.error('Failed to get archived bookmarks:', error);
      throw error;
    }
  }

  /**
   * Get all bookmarks for the current user (for search)
   */
  static async getBookmarks(): Promise<Bookmark[]> {
    try {
      const session = await AuthManager.requireSession();

      const { data, error } = await supabase
        .from('bookmarks')
        .select('*')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false });

      if (error) {
        if (error.code === 'PGRST301') { // JWT expired
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }

      return (data || []).map(this.normalizeBookmark);
    } catch (error) {
      console.error('Failed to get bookmarks:', error);
      throw error;
    }
  }

  /**
   * Update bookmark
   */
  static async updateBookmark(id: number, updates: Partial<Bookmark>): Promise<Bookmark> {
    try {
      const authHeader = await AuthManager.getAuthHeader();
      
      const { data, error } = await supabase
        .from('bookmarks')
        .update({
          title: updates.title,
          summary: updates.summary,
          tags: updates.tags,
          is_archived: updates.is_archived,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST301') {
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }

      return this.normalizeBookmark(data);
    } catch (error) {
      console.error('Failed to update bookmark:', error);
      throw error;
    }
  }

  /**
   * Delete bookmark
   */
  static async deleteBookmark(id: number): Promise<void> {
    try {
      const authHeader = await AuthManager.getAuthHeader();
      
      const { error } = await supabase
        .from('bookmarks')
        .delete()
        .eq('id', id);

      if (error) {
        if (error.code === 'PGRST301') {
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }
    } catch (error) {
      console.error('Failed to delete bookmark:', error);
      throw error;
    }
  }

  /**
   * Archive/unarchive bookmark
   */
  static async toggleArchive(id: number, isArchived: boolean): Promise<Bookmark> {
    return this.updateBookmark(id, { is_archived: isArchived });
  }

  /**
   * Mark bookmark as opened (update last_open timestamp)
   */
  static async markAsOpened(id: number): Promise<Bookmark> {
    try {
      const authHeader = await AuthManager.getAuthHeader();

      const { data, error } = await supabase
        .from('bookmarks')
        .update({
          last_open: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST301') {
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }

      return this.normalizeBookmark(data);
    } catch (error) {
      console.error('Failed to mark bookmark as opened:', error);
      throw error;
    }
  }

  /**
   * Get bookmark by ID
   */
  static async getBookmark(id: number): Promise<Bookmark | null> {
    try {
      const session = await AuthManager.requireSession();

      const { data, error } = await supabase
        .from('bookmarks')
        .select('*')
        .eq('id', id)
        .eq('user_id', session.user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // No rows returned
          return null;
        }
        if (error.code === 'PGRST301') {
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }

      return this.normalizeBookmark(data);
    } catch (error) {
      console.error('Failed to get bookmark:', error);
      throw error;
    }
  }

  /**
   * Get bookmarks that need re-embedding (for monitoring)
   */
  static async getBookmarksNeedingReembed(): Promise<Bookmark[]> {
    try {
      const session = await AuthManager.requireSession();

      const { data, error } = await supabase
        .from('bookmarks')
        .select('*')
        .eq('user_id', session.user.id)
        .eq('needs_reembed', true)
        .order('created_at', { ascending: false });

      if (error) {
        if (error.code === 'PGRST301') {
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }

      return (data || []).map(this.normalizeBookmark);
    } catch (error) {
      console.error('Failed to get bookmarks needing reembed:', error);
      throw error;
    }
  }

  /**
   * Subscribe to bookmark changes via Supabase Realtime
   */
  static subscribeToChanges(callback: (bookmark: Bookmark, eventType: 'INSERT' | 'UPDATE' | 'DELETE') => void) {
    return supabase
      .channel('bookmarks_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookmarks',
        },
        (payload: any) => {
          console.log('Bookmark change received:', payload);
          
          if (payload.eventType === 'DELETE') {
            callback(payload.old as Bookmark, 'DELETE');
          } else {
            const bookmark = this.normalizeBookmark(payload.new);
            callback(bookmark, payload.eventType as 'INSERT' | 'UPDATE');
          }
        }
      )
      .subscribe();
  }

  /**
   * Normalize bookmark data to match our interface
   */
  private static normalizeBookmark(data: any): Bookmark {
    return {
      id: data.id,
      url: data.url,
      title: data.title || undefined,
      summary: data.summary || undefined,
      tags: data.tags || undefined,
      is_archived: data.is_archived || false,
      created_at: data.created_at,
      updated_at: data.updated_at,
      needs_reembed: data.needs_reembed || false,
      last_open: data.last_open || null,
    };
  }

  /**
   * Handle API errors consistently
   */
  static handleAPIError(error: any): never {
    if (error.message === 'UNAUTHORIZED') {
      // Trigger re-authentication
      chrome.runtime.sendMessage({ type: 'AUTH_REQUIRED' });
      throw new Error('Authentication required');
    }
    
    if (error.code === 'NETWORK_ERROR') {
      throw new Error('Network error - please check your connection');
    }
    
    throw error;
  }
}
