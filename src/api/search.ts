import { Bookmark, SearchResult, SearchFilters } from '../types';
import { AuthManager } from '../utils/auth';
import { supabase } from '../lib/supabase';
import { env } from '../config/env';

export class SearchAPI {
  /**
   * Parse search query for filter tokens
   */
  static parseSearchQuery(query: string): { keyword: string; filters: SearchFilters } {
    const filters: SearchFilters = {};
    let keyword = query;

    // Extract filter tokens
    const filterPatterns = [
      { pattern: /#(\w+)/g, key: 'tags' },
      { pattern: /is:archived/g, key: 'is_archived', value: true },
      { pattern: /is:unarchived/g, key: 'is_archived', value: false },
      { pattern: /before:(\d{4}-\d{2}-\d{2})/g, key: 'before' },
      { pattern: /after:(\d{4}-\d{2}-\d{2})/g, key: 'after' },
    ];

    for (const { pattern, key, value } of filterPatterns) {
      const matches = [...keyword.matchAll(pattern)];
      
      if (matches.length > 0) {
        if (key === 'tags') {
          filters.tags = matches.map(match => match[1]);
        } else if (key === 'is_archived') {
          filters.is_archived = value as boolean;
        } else if (key === 'before' || key === 'after') {
          filters[key] = matches[0][1];
        }

        // Remove filter tokens from keyword
        keyword = keyword.replace(pattern, '').trim();
      }
    }

    return { keyword: keyword.trim(), filters };
  }

  /**
   * Simple keyword search using Postgres FTS
   */
  static async searchSimple(query: string): Promise<Bookmark[]> {
    try {
      const session = await AuthManager.requireSession();
      const { keyword, filters } = this.parseSearchQuery(query);

      // If no keyword and no filters, return empty results
      if (!keyword && Object.keys(filters).length === 0) {
        return [];
      }

      const { data, error } = await supabase.rpc('search_simple', {
        keyword: keyword || '',
        user_id: session.user.id,
        tags: filters.tags,
        is_archived: filters.is_archived,
        before: filters.before,
        after: filters.after,
      });

      if (error) {
        if (error.code === 'PGRST301') {
          throw new Error('UNAUTHORIZED');
        }
        throw error;
      }

      return (data || []).map(this.normalizeBookmark);
    } catch (error) {
      console.error('Failed to perform simple search:', error);
      throw error;
    }
  }

  /**
   * AI semantic search using pgvector + n8n
   */
  static async searchAI(query: string, from?: string, to?: string): Promise<SearchResult[]> {
    try {
      const authHeader = await AuthManager.getAuthHeader();
      
      const response = await fetch(`${env.n8n.baseUrl}/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader,
        },
        body: JSON.stringify({
          query,
          from,
          to,
        }),
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('UNAUTHORIZED');
        }
        if (response.status === 429) {
          throw new Error('RATE_LIMIT_EXCEEDED');
        }
        throw new Error(`AI search failed: ${response.statusText}`);
      }

      const results = await response.json();
      return results.map(this.normalizeSearchResult);
    } catch (error) {
      console.error('Failed to perform AI search:', error);
      throw error;
    }
  }

  /**
   * Combined search: simple + AI when appropriate
   */
  static async search(query: string): Promise<{
    simple: Bookmark[];
    ai: SearchResult[];
    hasFilters: boolean;
  }> {
    const { keyword, filters } = this.parseSearchQuery(query);
    const hasFilters = Object.keys(filters).length > 0;
    
    // Always perform simple search
    const simplePromise = this.searchSimple(query);
    
    // Perform AI search if query is long enough and no filter tokens
    const shouldUseAI = keyword.length >= 30 && !hasFilters;
    const aiPromise = shouldUseAI ? this.searchAI(keyword) : Promise.resolve([]);

    try {
      const [simple, ai] = await Promise.all([simplePromise, aiPromise]);
      
      return {
        simple,
        ai,
        hasFilters,
      };
    } catch (error) {
      console.error('Combined search failed:', error);
      
      // If AI search fails, still return simple results
      try {
        const simple = await simplePromise;
        return {
          simple,
          ai: [],
          hasFilters,
        };
      } catch (simpleError) {
        throw simpleError;
      }
    }
  }

  /**
   * Get search suggestions based on existing tags and titles
   */
  static async getSearchSuggestions(partial: string): Promise<string[]> {
    try {
      const session = await AuthManager.requireSession();
      
      if (partial.length < 2) {
        return [];
      }

      // Get tag suggestions
      const { data: tagData, error: tagError } = await supabase
        .from('bookmarks')
        .select('tags')
        .eq('user_id', session.user.id)
        .not('tags', 'is', null);

      if (tagError) {
        console.error('Failed to get tag suggestions:', tagError);
      }

      // Extract unique tags that match the partial
      const tags = new Set<string>();
      if (tagData) {
        tagData.forEach((row: any) => {
          if (row.tags) {
            row.tags.forEach((tag: string) => {
              if (tag.toLowerCase().includes(partial.toLowerCase())) {
                tags.add(`#${tag}`);
              }
            });
          }
        });
      }

      // Get title suggestions
      const { data: titleData, error: titleError } = await supabase
        .from('bookmarks')
        .select('title')
        .eq('user_id', session.user.id)
        .ilike('title', `%${partial}%`)
        .limit(5);

      if (titleError) {
        console.error('Failed to get title suggestions:', titleError);
      }

      const titles = titleData?.map((row: any) => row.title).filter(Boolean) || [];

      // Combine and return suggestions
      const suggestions = [...Array.from(tags), ...titles];
      return suggestions.slice(0, 10);
    } catch (error) {
      console.error('Failed to get search suggestions:', error);
      return [];
    }
  }

  /**
   * Get popular tags for the current user
   */
  static async getPopularTags(limit: number = 20): Promise<Array<{ tag: string; count: number }>> {
    try {
      const session = await AuthManager.requireSession();
      
      const { data, error } = await supabase
        .from('bookmarks')
        .select('tags')
        .eq('user_id', session.user.id)
        .not('tags', 'is', null);

      if (error) {
        throw error;
      }

      // Count tag occurrences
      const tagCounts = new Map<string, number>();
      
      data?.forEach((row: any) => {
        if (row.tags) {
          row.tags.forEach((tag: string) => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
          });
        }
      });

      // Sort by count and return top tags
      return Array.from(tagCounts.entries())
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);
    } catch (error) {
      console.error('Failed to get popular tags:', error);
      return [];
    }
  }

  /**
   * Normalize bookmark data from search results
   */
  private static normalizeBookmark(data: any): Bookmark {
    return {
      id: data.id,
      url: data.url,
      title: data.title || undefined,
      summary: data.summary || undefined,
      tags: data.tags || undefined,
      is_archived: data.is_archived || false,
      created_at: data.created_at,
      updated_at: data.updated_at,
      needs_reembed: data.needs_reembed || false,
    };
  }

  /**
   * Normalize search result data
   */
  private static normalizeSearchResult(data: any): SearchResult {
    return {
      id: data.id,
      title: data.title || data.url,
      url: data.url,
      snippet: data.snippet || data.summary || '',
      relevance: data.relevance || data.score || 0,
      summary: data.summary,
      tags: data.tags,
    };
  }

  /**
   * Handle search API errors
   */
  static handleSearchError(error: any): never {
    if (error.message === 'UNAUTHORIZED') {
      chrome.runtime.sendMessage({ type: 'AUTH_REQUIRED' });
      throw new Error('Authentication required');
    }
    
    if (error.message === 'RATE_LIMIT_EXCEEDED') {
      chrome.runtime.sendMessage({
        type: 'SHOW_TOAST',
        message: 'Daily AI-search limit reached',
        level: 'warning'
      });
      throw new Error('Daily AI-search limit reached');
    }
    
    throw error;
  }
}
