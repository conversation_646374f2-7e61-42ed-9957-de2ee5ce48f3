import { ClientMeta } from '../types';

/**
 * Simple and reliable page scraper
 */
export class PageScraper {
  /**
   * Extract essential metadata for bookmark card display
   */
  static extractPageMeta(): ClientMeta {
    console.log('🔍 Starting metadata extraction for bookmark card');

    const url = window.location.href;
    const timestamp = Date.now();

    // Parse domain info
    const urlObj = new URL(url);
    const domain = urlObj.hostname;

    // Extract title (best available from multiple sources)
    const title = this.extractBestTitle();

    // Extract summary/description (best available from multiple sources)
    const summary = this.extractBestSummary();

    // Extract logo/favicon
    const logo_url = this.extractLogo();

    // Check if page is bookmarkable
    const isBookmarkable = this.isBookmarkablePage();

    const result: ClientMeta = {
      url,
      title: title || document.title || 'Untitled',
      summary,
      logo_url,
      timestamp,
      domain,
      isBookmarkable,
    };

    console.log('✅ Metadata extraction completed:', result);
    return result;
  }

  /**
   * Extract best available title from multiple sources
   */
  static extractBestTitle(): string | undefined {
    console.log('📝 Extracting best title...');

    // Priority order: OG title > Twitter title > document title > h1
    const sources = [
      { name: 'Open Graph', selector: 'meta[property="og:title"]', attr: 'content' },
      { name: 'Twitter Card', selector: 'meta[name="twitter:title"]', attr: 'content' },
      { name: 'Document Title', value: document.title },
      { name: 'H1 Tag', selector: 'h1', attr: 'textContent' }
    ];

    for (const source of sources) {
      let title: string | undefined;

      if (source.value) {
        title = source.value;
      } else if (source.selector) {
        const element = document.querySelector(source.selector);
        if (element) {
          title = source.attr === 'content'
            ? element.getAttribute('content') || undefined
            : element.textContent || undefined;
        }
      }

      if (title && title.trim()) {
        console.log(`✅ Found title from ${source.name}:`, title.trim());
        return title.trim();
      }
    }

    console.log('❌ No title found');
    return undefined;
  }

  /**
   * Extract best available summary/description from multiple sources
   */
  static extractBestSummary(): string | undefined {
    console.log('📝 Extracting best summary...');

    // Priority order: OG description > meta description > Twitter description
    const sources = [
      { name: 'Open Graph', selector: 'meta[property="og:description"]' },
      { name: 'Meta Description', selector: 'meta[name="description"]' },
      { name: 'Twitter Card', selector: 'meta[name="twitter:description"]' }
    ];

    for (const source of sources) {
      const element = document.querySelector(source.selector);
      if (element) {
        const summary = element.getAttribute('content');
        if (summary && summary.trim()) {
          console.log(`✅ Found summary from ${source.name}:`, summary.substring(0, 100) + '...');
          return summary.trim();
        }
      }
    }

    console.log('❌ No summary found');
    return undefined;
  }

  /**
   * Extract logo/favicon URL from multiple sources
   */
  static extractLogo(): string | undefined {
    console.log('📝 Extracting logo/favicon...');

    // Priority order: Apple touch icon > icon > shortcut icon > favicon.ico
    const sources = [
      { name: 'Apple Touch Icon', selector: 'link[rel="apple-touch-icon"]' },
      { name: 'Icon', selector: 'link[rel="icon"]' },
      { name: 'Shortcut Icon', selector: 'link[rel="shortcut icon"]' },
      { name: 'Favicon', selector: 'link[rel="favicon"]' }
    ];

    for (const source of sources) {
      const element = document.querySelector(source.selector) as HTMLLinkElement;
      if (element?.href) {
        console.log(`✅ Found logo from ${source.name}:`, element.href);
        return element.href;
      }
    }

    // Fallback to default favicon location
    const defaultFavicon = `${window.location.origin}/favicon.ico`;
    console.log('⚠️ Using default favicon:', defaultFavicon);
    return defaultFavicon;
  }

  /**
   * Check if page is bookmarkable
   */
  static isBookmarkablePage(): boolean {
    const url = window.location.href;
    const unbookmarkableProtocols = ['chrome:', 'chrome-extension:', 'moz-extension:', 'about:', 'data:', 'javascript:', 'file:'];
    return !unbookmarkableProtocols.some(protocol => url.startsWith(protocol));
  }
}

// Ensure we don't load multiple times
if (!(window as any).linkInboxContentScriptLoaded) {
  (window as any).linkInboxContentScriptLoaded = true;

  console.log('🚀 Link Inbox Content Script Loading...');

  // Message listener for background script
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 Content script received message:', message);

    if (message.type === 'EXTRACT_PAGE_META') {
      console.log('📨 Processing metadata extraction request');

      try {
        const meta = PageScraper.extractPageMeta();
        console.log('✅ Sending metadata response:', meta);
        sendResponse({
          success: true,
          data: { meta }
        });
      } catch (error) {
        console.error('❌ Metadata extraction failed:', error);
        sendResponse({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return true; // Keep message channel open
  });

  // Debug function for manual testing
  (window as any).debugMetadata = () => {
    console.log('🔧 Manual metadata extraction');
    return PageScraper.extractPageMeta();
  };

  // Test function to verify content script is working
  (window as any).testContentScript = () => {
    console.log('✅ Content script is working!');
    console.log('📍 Current URL:', window.location.href);
    console.log('📄 Document title:', document.title);
    return {
      loaded: true,
      url: window.location.href,
      title: document.title
    };
  };

  // Send a message to background to confirm loading
  try {
    chrome.runtime.sendMessage({
      type: 'CONTENT_SCRIPT_LOADED',
      url: window.location.href
    });
  } catch (error) {
    console.log('⚠️ Could not send load confirmation to background:', error);
  }

  console.log('✅ Link Inbox Content Script Loaded Successfully');
} else {
  console.log('⚠️ Content script already loaded, skipping...');
}
