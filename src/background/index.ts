// Import polyfills first to prevent require errors
import '../utils/polyfills';

import { AuthManager } from '../utils/auth';
import { NetworkManager } from '../utils/networkStatus';
import { BookmarkAPI } from '../api/bookmark';
import { Bookmark, ClientMeta, Session } from '../types';
import { validateEnvironment } from '../config/env';
import { supabase } from '../lib/supabase';

// Extension installation handler
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('Smart Bookmarks extension installed/updated:', details.reason);
  
  if (details.reason === 'install') {
    await initializeExtension();
  }
});

// Extension startup handler
chrome.runtime.onStartup.addListener(async () => {
  console.log('Smart Bookmarks extension started');
  await initializeExtension();
});

// Message handler for communication with popup/content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  handleMessage(message, sender, sendResponse);
  return true; // Keep message channel open for async response
});

// Context menu setup
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'save-bookmark',
    title: 'Save to Smart Bookmarks',
    contexts: ['page'],
  });
});

// Context menu click handler
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  if (info.menuItemId === 'save-bookmark' && tab) {
    await saveCurrentTab(tab);
  }
});

// Network state change listener - simplified for online-only mode
chrome.runtime.onConnect.addListener(() => {
  // Just log network status
  console.log('Extension connected, network status:', navigator.onLine ? 'online' : 'offline');
});

/**
 * Initialize extension
 */
async function initializeExtension(): Promise<void> {
  try {
    // Validate environment variables
    validateEnvironment();

    // Initialize Supabase auth listener
    supabase.auth.onAuthStateChange(async (event: any, session: any) => {
      console.log('Auth state changed:', event);

      if (event === 'SIGNED_IN' && session) {
        const customSession = {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_in: session.expires_in || 3600,
          expires_at: session.expires_at ? session.expires_at * 1000 : Date.now() + 3600000,
          user: {
            id: session.user.id,
            name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
            email: session.user.email || '',
            avatar: session.user.user_metadata?.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.user.email || 'User')}&background=3b82f6&color=fff`,
          },
        };
        await chrome.storage.local.set({ supabase_session: customSession });
      } else if (event === 'SIGNED_OUT') {
        await chrome.storage.local.remove('supabase_session');
      }
    });

    // Initialize authentication
    await AuthManager.initializeAuth();

    // Initialize network manager
    NetworkManager.initialize({
      onOnline: () => {
        console.log('Network connection restored');
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icon/48.png',
          title: 'Back Online',
          message: 'You can now save bookmarks again.',
        });
      },
      onOffline: () => {
        console.log('Network connection lost');
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icon/48.png',
          title: 'Offline',
          message: 'Bookmark saving is disabled while offline.',
        });
      },
    });

    // Subscribe to bookmark changes
    subscribeToBookmarkChanges();

    console.log('Extension initialized successfully');
  } catch (error) {
    console.error('Failed to initialize extension:', error);
  }
}

/**
 * Handle messages from popup/content scripts
 */
async function handleMessage(
  message: any,
  sender: chrome.runtime.MessageSender,
  sendResponse: (response: any) => void
): Promise<void> {
  try {
    switch (message.type) {
      case 'GET_SESSION':
        const session = await AuthManager.getSession();
        sendResponse({ success: true, session });
        break;
        
      case 'SAVE_CURRENT_TAB':
        const result = await saveCurrentTabFromMessage();
        sendResponse({ success: true, bookmark: result });
        break;
        
      case 'EXTRACT_PAGE_META':
        const metaResult = await extractPageMeta(sender.tab?.id);
        sendResponse({ success: true, data: metaResult });
        break;
        
      case 'AUTH_REQUIRED':
        // Trigger authentication flow
        chrome.action.openPopup();
        sendResponse({ success: true });
        break;
        
      case 'SHOW_TOAST':
        // Show notification
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icon/48.png',
          title: 'Smart Bookmarks',
          message: message.message,
        });
        sendResponse({ success: true });
        break;
        
      case 'AUTH_CALLBACK':
        // OAuth callback from auth page - just acknowledge receipt
        // The actual processing is handled by AuthManager
        sendResponse({ success: true });
        break;

      case 'PING':
        sendResponse({ success: true, message: 'pong' });
        break;

      case 'CONTENT_SCRIPT_LOADED':
        console.log('✅ Content script loaded on:', message.url);
        sendResponse({ success: true });
        break;

      case 'MANUAL_META_RESPONSE':
        console.log('📥 Received manual metadata response:', message.data);
        sendResponse({ success: true });
        break;

      default:
        sendResponse({ success: false, error: 'Unknown message type' });
    }
  } catch (error) {
    console.error('Error handling message:', error);
    sendResponse({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}

/**
 * Save current active tab as bookmark
 */
async function saveCurrentTabFromMessage(): Promise<Bookmark> {
  const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
  const tab = tabs[0];
  
  if (!tab || !tab.url || !tab.title) {
    throw new Error('No active tab found or tab has no URL/title');
  }
  
  return await saveCurrentTab(tab);
}

/**
 * Save a specific tab as bookmark
 */
async function saveCurrentTab(tab: chrome.tabs.Tab): Promise<Bookmark> {
  if (!tab.url || !tab.title || !tab.id) {
    throw new Error('Tab has no URL, title, or ID');
  }

  // Check if online first - use both navigator.onLine and actual connectivity test
  if (!NetworkManager.isOnline()) {
    NetworkManager.showOfflineNotification('save bookmark');
    throw new Error('Cannot save bookmark while offline');
  }

  // Additional connectivity test for Service Worker environment
  const hasConnectivity = await NetworkManager.testConnectivity();
  if (!hasConnectivity) {
    NetworkManager.showOfflineNotification('save bookmark');
    throw new Error('Cannot save bookmark - no internet connection');
  }

  try {
    console.log('🔍 Background: Starting metadata extraction for tab:', tab.url);

    // Extract comprehensive page metadata using content script
    const metaData = await extractPageMeta(tab.id);
    console.log('📊 Background: Raw metadata from content script:', metaData);

    // Use extracted metadata if available, otherwise fall back to tab info
    const clientMeta: ClientMeta = metaData?.meta || {
      url: tab.url,
      title: tab.title,
      timestamp: Date.now(),
    };

    // Ensure URL and timestamp are always set
    clientMeta.url = tab.url;
    clientMeta.timestamp = Date.now();

    console.log('📋 Background: Final client metadata:', clientMeta);

    // Add bookmark via API (online-only)
    console.log('🚀 Background: Sending to BookmarkAPI...');
    const bookmark = await BookmarkAPI.addBookmark(clientMeta);

    // Show success notification
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icon/48.png',
      title: 'Bookmark Saved',
      message: `"${bookmark.title || bookmark.url}" has been saved.`,
    });

    return bookmark;
  } catch (error) {
    console.error('Failed to save bookmark:', error);

    // Show error notification
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icon/48.png',
      title: 'Bookmark Save Failed',
      message: 'Failed to save bookmark. Please try again.',
    });

    throw error;
  }
}

/**
 * Extract page metadata using content script
 */
async function extractPageMeta(tabId?: number): Promise<any> {
  if (!tabId) {
    console.log('❌ Background: No tab ID provided');
    return null;
  }

  console.log('📤 Background: Sending EXTRACT_PAGE_META message to tab', tabId);

  try {
    // First, try to inject the content script if it's not already there
    try {
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ['src/content/scraper.js']
      });
      console.log('✅ Background: Content script injected');
    } catch (injectionError) {
      console.log('⚠️ Background: Content script injection failed (might already be injected):', injectionError);
    }

    // Wait a bit for the script to load
    await new Promise(resolve => setTimeout(resolve, 100));

    const response = await chrome.tabs.sendMessage(tabId, { type: 'EXTRACT_PAGE_META' });
    console.log('📥 Background: Received response from content script:', response);

    if (response && response.success) {
      console.log('✅ Background: Successfully extracted metadata');
      return response.data;
    } else {
      console.log('❌ Background: Content script returned error:', response?.error || 'No response');
      return null;
    }
  } catch (error) {
    console.error('❌ Background: Failed to communicate with content script:', error);

    // Try manual injection as fallback
    try {
      console.log('🔄 Background: Trying manual content script injection...');
      await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          console.log('🔧 Manual script: Injected into page');
          (window as any).manualExtractMeta = () => {
            const url = window.location.href;

            // Extract title (best available)
            const title = document.querySelector('meta[property="og:title"]')?.getAttribute('content') ||
                         document.querySelector('meta[name="twitter:title"]')?.getAttribute('content') ||
                         document.title ||
                         'Untitled';

            // Extract summary (best available)
            const summary = document.querySelector('meta[property="og:description"]')?.getAttribute('content') ||
                           document.querySelector('meta[name="description"]')?.getAttribute('content') ||
                           document.querySelector('meta[name="twitter:description"]')?.getAttribute('content') ||
                           undefined;

            // Extract logo (best available)
            const logoElement = document.querySelector('link[rel="apple-touch-icon"], link[rel="icon"], link[rel="shortcut icon"]') as HTMLLinkElement;
            const logo_url = logoElement?.href || `${window.location.origin}/favicon.ico`;

            return {
              url,
              title,
              summary,
              logo_url,
              timestamp: Date.now(),
              domain: window.location.hostname,
              isBookmarkable: true
            };
          };

          // Send response back
          chrome.runtime.sendMessage({
            type: 'MANUAL_META_RESPONSE',
            data: (window as any).manualExtractMeta()
          });
        }
      });

      return new Promise((resolve) => {
        const listener = (message: any) => {
          if (message.type === 'MANUAL_META_RESPONSE') {
            chrome.runtime.onMessage.removeListener(listener);
            resolve({ meta: message.data });
          }
        };
        chrome.runtime.onMessage.addListener(listener);

        // Timeout after 5 seconds
        setTimeout(() => {
          chrome.runtime.onMessage.removeListener(listener);
          resolve(null);
        }, 5000);
      });

    } catch (manualError) {
      console.error('❌ Background: Manual injection also failed:', manualError);
      return null;
    }
  }
}

/**
 * Subscribe to bookmark changes via Supabase Realtime
 */
function subscribeToBookmarkChanges(): void {
  try {
    BookmarkAPI.subscribeToChanges((bookmark, eventType) => {
      console.log('Bookmark change received:', eventType, bookmark);

      // Notify popup about the change
      chrome.runtime.sendMessage({
        type: 'BOOKMARK_CHANGED',
        bookmark,
        eventType,
      }).catch(() => {
        // Popup might not be open, ignore error
      });
    });
  } catch (error) {
    console.error('Failed to subscribe to bookmark changes:', error);
  }
}

/**
 * Monitor bookmarks that need re-embedding
 */
async function monitorReembedStatus(): Promise<void> {
  try {
    const bookmarksNeedingReembed = await BookmarkAPI.getBookmarksNeedingReembed();

    // Check for bookmarks that have been processing for too long (>15 minutes)
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString();

    const staleBookmarks = bookmarksNeedingReembed.filter(
      bookmark => bookmark.created_at < fifteenMinutesAgo
    );

    if (staleBookmarks.length > 0) {
      console.warn(`Found ${staleBookmarks.length} bookmarks with failed processing`);

      // Notify user about processing failures
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icon/48.png',
        title: 'Processing Issues',
        message: `${staleBookmarks.length} bookmark(s) failed to process completely.`,
      });
    }
  } catch (error) {
    console.error('Failed to monitor reembed status:', error);
  }
}

// Monitor reembed status every 5 minutes
setInterval(monitorReembedStatus, 5 * 60 * 1000);

// Export for testing
export {
  initializeExtension,
  handleMessage,
  saveCurrentTab,
  extractPageMeta,
};
