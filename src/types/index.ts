export interface User {
  id: string;
  name: string;
  avatar: string;
  email?: string;
  plan_id?: string;
  created_at?: string;
  last_login_at?: string;
  deleted_at?: string;
  archived_count?: number;
}

export interface Bookmark {
  id: number;
  url: string;
  title?: string;
  summary?: string;
  tags?: string[];
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  needs_reembed?: boolean;
  last_open?: string | null;
  logo_url?: string | null;
}

export interface Embedding {
  bookmark_id: number;
  embedding: number[];
  embedded_at: string;
}

export interface SearchResult {
  id: number;
  title: string;
  url: string;
  snippet: string;
  relevance: number;
  summary?: string;
  tags?: string[];
}



export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
}

export interface Session {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at: number;
  user: User;
}

export interface ClientMeta {
  url: string;
  title?: string;
  summary?: string;  // description을 summary로 변경 (Bookmark 인터페이스와 일치)
  logo_url?: string; // 페이지 로고/파비콘 URL
  timestamp: number;
  domain?: string;
  isBookmarkable?: boolean;
}

export interface SearchFilters {
  tags?: string[];
  is_archived?: boolean;
  before?: string;
  after?: string;
  includeArchived?: boolean;
}
